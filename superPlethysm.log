This is pdfTeX, Version 3.141592653-2.6-1.40.24 (MiKTeX 22.1) (preloaded format=pdflatex 2025.2.12)  22 MAY 2025 03:15
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/Users/<USER>/Dropbox/super_schur/Notes/Super plethysm/superPlethysm.tex"
(/Users/<USER>/Dropbox/super_schur/Notes/Super plethysm/superPlethysm.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-10-09>
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/size10.clo
File: size10.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/graphics.sty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen142
\Gin@req@width=\dimen143
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count268
\calc@Bcount=\count269
\calc@Adimen=\dimen144
\calc@Bdimen=\dimen145
\calc@Askip=\skip51
\calc@Bskip=\skip52
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count270
\calc@Cskip=\skip53
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip54

For additional information on amsmath, use the `?' option.
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen146
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen147
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen148
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks19
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks20
\eqnshift@=\dimen149
\alignsep@=\dimen150
\tagshift@=\dimen151
\tagwidth@=\dimen152
\totwidth@=\dimen153
\lineht@=\dimen154
\@envbody=\toks21
\multlinegap=\skip55
\multlinetaggap=\skip56
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
\g_MT_multlinerow_int=\count283
\l_MT_multwidth_dim=\dimen155
\origjot=\skip57
\l_MT_shortvdotswithinadjustabove_dim=\dimen156
\l_MT_shortvdotswithinadjustbelow_dim=\dimen157
\l_MT_above_intertext_sep=\dimen158
\l_MT_below_intertext_sep=\dimen159
\l_MT_above_shortintertext_sep=\dimen160
\l_MT_below_shortintertext_sep=\dimen161
\xmathstrut@box=\box54
\xmathstrut@dim=\dimen162
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks23
\thm@bodyfont=\toks24
\thm@headfont=\toks25
\thm@notefont=\toks26
\thm@headpunct=\toks27
\thm@preskip=\skip58
\thm@postskip=\skip59
\thm@headsep=\skip60
\dth@everypar=\toks28
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/ytableau/ytableau.sty
Package: ytableau 2021/06/12 v1.4 Many-featured Young tableaux and Young diagrams
\tableaux@YT=\box55
\thistableau@YT=\box56
\refhtdp@YT=\box57
\toks@YT=\toks29
\opttoksa@YT=\toks30
\opttoksb@YT=\toks31
\boxdim@YT=\dimen163
\tableauwd@YT=\dimen164
\count@YT=\count284
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgfkeys.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks32
\pgfkeys@temptoks=\toks33
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks34
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgfopts/pgfopts.sty
Package: pgfopts 2014/07/10 v2.1a LaTeX package options with pgfkeys
\pgfopts@list@add@a@toks=\toks35
\pgfopts@list@add@b@toks=\toks36
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip61
\multirow@cntb=\count285
\multirow@dima=\skip62
\bigstrutjot=\dimen165
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-07-10 v7.01j Hypertext links for LaTeX
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count286
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count287
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen166
\Hy@linkcounter=\count288
\Hy@pagecounter=\count289
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-07-10 v7.01j Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count290
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hyperref/puenc.def
File: puenc.def 2024-07-10 v7.01j Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count291
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen167
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count292
\Field@Width=\dimen168
\Fld@charsize=\dimen169
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count293
\c@Item=\count294
\c@Hfootnote=\count295
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-07-10 v7.01j Hyperref driver for pdfTeX
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count296
\c@bookmark@seq@number=\count297
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip63
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment') (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip64
\bibsep=\skip65
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count298
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/blindtext/blindtext.sty
Package: blindtext 2012/01/06 V2.0 blindtext-Package
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
)
\c@blindtext=\count299
\c@Blindtext=\count300
\c@blind@countparstart=\count301
\blind@countxx=\count302
\blindtext@numBlindtext=\count303
\blind@countyy=\count304
\c@blindlist=\count305
\c@blindlistlevel=\count306
\c@blindlist@level=\count307
\blind@listitem=\count308
\c@blind@listcount=\count309
\c@blind@levelcount=\count310
\blind@mathformula=\count311
\blind@Mathformula=\count312
\c@blind@randomcount=\count313
\c@blind@randommax=\count314
\c@blind@pangramcount=\count315
\c@blind@pangrammax=\count316
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip66
\enit@outerparindent=\dimen170
\enit@toks=\toks37
\enit@inbox=\box58
\enit@count@id=\count317
\enitdp@description=\count318
)
\c@theorem=\count319
\c@lemma=\count320
\c@proposition=\count321
\c@corollary=\count322
\c@definition=\count323
\c@example=\count324
\c@remark=\count325
\c@question=\count326
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tikz-cd/tikz-cd.sty
Package: tikz-cd 2021/05/04 v1.0 Commutative diagrams with TikZ
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/frontendlayer/tikz.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/basiclayer/pgf.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgfrcs.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks38
\pgfutil@tempdima=\dimen171
\pgfutil@tempdimb=\dimen172
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box59
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgfrcs.code.tex (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/basiclayer/pgfcore.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/systemlayer/pgfsys.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen173
\pgf@y=\dimen174
\pgf@xa=\dimen175
\pgf@ya=\dimen176
\pgf@xb=\dimen177
\pgf@yb=\dimen178
\pgf@xc=\dimen179
\pgf@yc=\dimen180
\pgf@xd=\dimen181
\pgf@yd=\dimen182
\w@pgf@writea=\write4
\r@pgf@reada=\read2
\c@pgf@counta=\count327
\c@pgf@countb=\count328
\c@pgf@countc=\count329
\c@pgf@countd=\count330
\t@pgf@toka=\toks39
\t@pgf@tokb=\toks40
\t@pgf@tokc=\toks41
\pgf@sys@id@count=\count331
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count332
\pgfsyssoftpath@bigbuffer@items=\count333
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmath.code.tex (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathutil.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen183
\pgfmath@count=\count334
\pgfmath@box=\box60
\pgfmath@toks=\toks42
\pgfmath@stack@operand=\toks43
\pgfmath@stack@operation=\toks44
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathcalc.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count335
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfint.code.tex) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen184
\pgf@picmaxx=\dimen185
\pgf@picminy=\dimen186
\pgf@picmaxy=\dimen187
\pgf@pathminx=\dimen188
\pgf@pathmaxx=\dimen189
\pgf@pathminy=\dimen190
\pgf@pathmaxy=\dimen191
\pgf@xx=\dimen192
\pgf@xy=\dimen193
\pgf@yx=\dimen194
\pgf@yy=\dimen195
\pgf@zx=\dimen196
\pgf@zy=\dimen197
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen198
\pgf@path@lasty=\dimen199
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen256
\pgf@shorten@start@additional=\dimen257
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count336
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen258
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen259
\pgf@pt@y=\dimen260
\pgf@pt@temp=\dimen261
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen262
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen263
\pgf@sys@shading@range@num=\count337
\pgf@shadingcount=\count338
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen264
\pgf@nodesepend=\dimen265
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/utilities/pgffor.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/pgf/math/pgfmath.sty (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/math/pgfmath.code.tex)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen266
\pgffor@skip=\dimen267
\pgffor@stack=\toks45
\pgffor@toks=\toks46
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count339
\pgfplotmarksize=\dimen268
)
\tikz@lastx=\dimen269
\tikz@lasty=\dimen270
\tikz@lastxsaved=\dimen271
\tikz@lastysaved=\dimen272
\tikz@lastmovetox=\dimen273
\tikz@lastmovetoy=\dimen274
\tikzleveldistance=\dimen275
\tikzsiblingdistance=\dimen276
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count340
\tikznumberofchildren=\count341
\tikznumberofcurrentchild=\count342
\tikz@fig@count=\count343
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count344
\pgfmatrixcurrentcolumn=\count345
\pgf@matrix@numberofcolumns=\count346
)
\tikz@expandcount=\count347
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tikz-cd/tikzlibrarycd.code.tex (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarymatrix.code.tex
File: tikzlibrarymatrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryquotes.code.tex
File: tikzlibraryquotes.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/pgflibraryarrows.meta.code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen277
\pgfarrowlength=\dimen278
\pgfarrowwidth=\dimen279
\pgfarrowlinewidth=\dimen280
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/graphs/tikzlibrarygraphs.code.tex
File: tikzlibrarygraphs.code.tex 2023-01-15 v3.1.10 (3.1.10)
\tikz@lib@auto@number=\count348
\tikz@qnode@count=\count349
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.callouts.code.tex (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.callouts.code.tex)) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pgf/libraries/shapes/pgflibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box70
\pgfnodeparttwobox=\box71
\pgfnodepartthreebox=\box72
\pgfnodepartfourbox=\box73
\pgfnodeparttwentybox=\box74
\pgfnodepartnineteenbox=\box75
\pgfnodeparteighteenbox=\box76
\pgfnodepartseventeenbox=\box77
\pgfnodepartsixteenbox=\box78
\pgfnodepartfifteenbox=\box79
\pgfnodepartfourteenbox=\box80
\pgfnodepartthirteenbox=\box81
\pgfnodeparttwelvebox=\box82
\pgfnodepartelevenbox=\box83
\pgfnodeparttenbox=\box84
\pgfnodepartninebox=\box85
\pgfnodeparteightbox=\box86
\pgfnodepartsevenbox=\box87
\pgfnodepartsixbox=\box88
\pgfnodepartfivebox=\box89
))) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count350
\l__pdf_internal_box=\box90
) (superPlethysm.aux)
\openout1 = `superPlethysm.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count351
\scratchdimen=\dimen281
\scratchbox=\box91
\nofMPsegments=\count352
\nofMParguments=\count353
\everyMPshowfont=\toks47
\MPscratchCnt=\count354
\MPscratchDim=\dimen282
\MPnumerator=\count355
\makeMPintoPDFobject=\count356
\everyMPtoPDFconversion=\toks48
) (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/00miktex/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring OFF on input line 37.
 (superPlethysm.out) (superPlethysm.out)
\@outlinefile=\write5
\openout5 = `superPlethysm.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 39.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 39.
 (/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: No \author given.



[1

{/Users/<USER>/Library/Application Support/MiKTeX/texmfs/data/fonts/map/pdftex/pdftex.map}] (superPlethysm.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-10-09>
 ***********
Package rerunfilecheck Info: File `superPlethysm.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 25434 strings out of 473897
 522187 string characters out of 5714229
 910086 words of memory out of 5000000
 47885 multiletter control sequences out of 15000+600000
 565058 words of font info for 64 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 95i,6n,101p,614b,441s stack positions out of 10000i,1000n,20000p,200000b,200000s
</Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmmi10.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmmi7.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmr10.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmr12.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmr17.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmr7.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmsy10.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmsy7.pfb></Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/fonts/type1/public/amsfonts/cm/cmti10.pfb>
Output written on superPlethysm.pdf (1 page, 97288 bytes).
PDF statistics:
 59 PDF objects out of 1000 (max. 8388607)
 2 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

